import { style } from "@vanilla-extract/css"
import { theme } from "../theme.css"

export const formStyles = style({
  display: "flex",
  flexDirection: "column",
  gap: theme.space[3],
  maxWidth: "800px",
  width: "100%",
})

export const formRowStyles = style({
  display: "flex",
  flexDirection: "column",
  gap: theme.space[3],
  "@media": {
    "(min-width: 768px)": {
      flexDirection: "row",
    },
  },
})

export const formFieldStyles = style({
  flex: 1,
  minWidth: 0, // Prevents flex items from overflowing
})

export const inputStyles = style({
  padding: theme.space[3],
  border: `1px solid black`,
  borderRadius: theme.radii.button,
  fontSize: theme.fontSizes[2],
  fontFamily: theme.fonts.text,
  width: "100%",
  boxSizing: "border-box",
  ":focus": {
    outline: "none",
    borderColor: theme.colors.primary,
    boxShadow: `0 0 0 2px ${theme.colors.primary}20`,
  },
})

export const textareaStyles = style([
  inputStyles,
  {
    minHeight: "120px",
    resize: "vertical",
  },
])

export const labelStyles = style({
  fontSize: theme.fontSizes[2],
  fontWeight: theme.fontWeights.medium,
  marginBottom: theme.space[1],
  display: "block",
})

export const requiredStyles = style({
  color: theme.colors.primary,
})

export const submitButtonStyles = style([
  inputStyles,
  {
    backgroundColor: theme.colors.primary,
    color: "white",
    border: "none",
    cursor: "pointer",
    fontWeight: theme.fontWeights.medium,
    ":hover": {
      opacity: 0.9,
    },
    ":disabled": {
      opacity: 0.6,
      cursor: "not-allowed",
    },
  },
])

export const successMessageStyles = style({
  marginBottom: theme.space[3],
  padding: theme.space[3],
  backgroundColor: "#d4edda",
  border: "1px solid #c3e6cb",
  color: "#155724",
  borderRadius: theme.radii.button,
})

export const captchaContainerStyles = style({
  display: "flex",
  alignItems: "center",
  gap: theme.space[2],
  marginBottom: theme.space[2],
})

export const captchaQuestionStyles = style({
  fontSize: theme.fontSizes[3],
  fontWeight: theme.fontWeights.bold,
  color: theme.colors.text,
})

export const captchaRefreshStyles = style({
  background: "none",
  border: `1px solid ${theme.colors.border}`,
  borderRadius: theme.radii.button,
  padding: `${theme.space[1]} ${theme.space[2]}`,
  cursor: "pointer",
  fontSize: theme.fontSizes[1],
  ":hover": {
    backgroundColor: theme.colors.muted,
  },
})

export const captchaInputStyles = style([
  inputStyles,
  {
    maxWidth: "120px",
  },
])
