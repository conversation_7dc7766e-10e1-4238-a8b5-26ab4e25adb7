import { style } from "@vanilla-extract/css"
import { theme } from "../theme.css"

export const formStyles = style({
  display: "flex",
  flexDirection: "column",
  gap: theme.space[3],
  maxWidth: "800px",
  width: "100%",
})

export const formRowStyles = style({
  display: "flex",
  flexDirection: "column",
  gap: theme.space[3],
  "@media": {
    "(min-width: 768px)": {
      flexDirection: "row",
    },
  },
})

export const inputStyles = style({
  padding: theme.space[3],
  border: `1px solid black`,
  borderRadius: theme.radii.button,
  fontSize: theme.fontSizes[2],
  fontFamily: theme.fonts.text,
  width: "100%",
  boxSizing: "border-box",
  ":focus": {
    outline: "none",
    borderColor: theme.colors.primary,
    boxShadow: `0 0 0 2px ${theme.colors.primary}20`,
  },
})

export const textareaStyles = style([
  inputStyles,
  {
    minHeight: "120px",
    resize: "vertical",
  },
])

export const labelStyles = style({
  fontSize: theme.fontSizes[2],
  fontWeight: theme.fontWeights.medium,
  marginBottom: theme.space[1],
  display: "block",
})

export const requiredStyles = style({
  color: theme.colors.primary,
})

export const submitButtonStyles = style([
  inputStyles,
  {
    backgroundColor: theme.colors.primary,
    color: "white",
    border: "none",
    cursor: "pointer",
    fontWeight: theme.fontWeights.medium,
    ":hover": {
      opacity: 0.9,
    },
  },
])
