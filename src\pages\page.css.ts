import { style } from "@vanilla-extract/css"
import { theme } from "../theme.css"

export const contactSection = style({
  textAlign: "center",
  padding: `${theme.space[4]} 0`,
  borderTop: `1px solid ${theme.colors.muted}`,
  borderBottom: `1px solid ${theme.colors.muted}`,
  margin: `${theme.space[4]} 0`,
})

export const contactHeading = style({
  marginBottom: theme.space[3],
  color: theme.colors.heading,
})

export const contactDescription = style({
  marginBottom: theme.space[3],
  color: theme.colors.text,
})

export const emailLink = style({
  color: theme.colors.primary,
  textDecoration: "none",
  borderBottom: `2px solid ${theme.colors.primary}`,
  fontSize: theme.fontSizes[4],
  fontWeight: theme.fontWeights.medium,
  transition: "all 0.2s ease",
  ":hover": {
    opacity: 0.8,
    borderBottomColor: "transparent",
  },
  ":focus": {
    outline: `2px solid ${theme.colors.primary}`,
    outlineOffset: "2px",
  },
})
