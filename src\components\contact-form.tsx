import * as React from "react"
import { Box, Text } from "./ui"
import * as styles from "./contact-form.css"
import Re<PERSON><PERSON><PERSON><PERSON> from "react-google-recaptcha"

export default function ContactForm() {
  const [isSubmitted, setIsSubmitted] = React.useState(false)
  const [isSubmitting, setIsSubmitting] = React.useState(false)
  const [recaptchaToken, setRecaptchaToken] = React.useState<string | null>(null)
  const recaptchaRef = React.useRef<ReCAPTCHA>(null)

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    // Validate reCAPTCHA
    if (!recaptchaToken) {
      alert("Veuillez compléter la vérification reCAPTCHA.")
      return
    }

    setIsSubmitting(true)

    const form = e.currentTarget
    const formData = new FormData(form)

    // Add reCAPTCHA token to form data
    formData.append("g-recaptcha-response", recaptchaToken)

    try {
      const response = await fetch("/", {
        method: "POST",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: new URLSearchParams(formData as any).toString(),
      })

      if (response.ok) {
        setIsSubmitted(true)
        form.reset()
        setRecaptchaToken(null)
        recaptchaRef.current?.reset()
        // Hide success message after 5 seconds
        setTimeout(() => setIsSubmitted(false), 5000)
      } else {
        throw new Error("Form submission failed")
      }
    } catch (error) {
      alert("Une erreur s'est produite. Veuillez réessayer.")
      setRecaptchaToken(null)
      recaptchaRef.current?.reset()
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Box paddingY={4}>
      {isSubmitted && (
        <div className={styles.successMessageStyles}>
          <Text center>
            ✅ Merci ! Votre message a été envoyé avec succès. Nous vous répondrons bientôt.
          </Text>
        </div>
      )}

      <form
        name="contact"
        method="POST"
        data-netlify="true"
        data-netlify-honeypot="bot-field"
        className={styles.formStyles}
        onSubmit={handleSubmit}
      >
        {/* Hidden field for Netlify */}
        <input type="hidden" name="form-name" value="contact" />

        {/* Honeypot field for spam protection */}
        <div style={{ display: "none" }}>
          <label>
            Don't fill this out if you're human: <input name="bot-field" />
          </label>
        </div>

        <div className={styles.formRowStyles}>
          <div className={styles.formFieldStyles}>
            <label htmlFor="name" className={styles.labelStyles}>
              Nom <span className={styles.requiredStyles}>*</span>
            </label>
            <input
              type="text"
              id="name"
              name="name"
              required
              className={styles.inputStyles}
              placeholder="Votre nom complet"
            />
          </div>

          <div className={styles.formFieldStyles}>
            <label htmlFor="email" className={styles.labelStyles}>
              Email <span className={styles.requiredStyles}>*</span>
            </label>
            <input
              type="email"
              id="email"
              name="email"
              required
              className={styles.inputStyles}
              placeholder="<EMAIL>"
            />
          </div>
        </div>

        <div>
          <label htmlFor="subject" className={styles.labelStyles}>
            Sujet <span className={styles.requiredStyles}>*</span>
          </label>
          <input
            type="text"
            id="subject"
            name="subject"
            required
            className={styles.inputStyles}
            placeholder="Objet de votre message"
          />
        </div>

        <div>
          <label htmlFor="message" className={styles.labelStyles}>
            Message <span className={styles.requiredStyles}>*</span>
          </label>
          <textarea
            id="message"
            name="message"
            required
            className={styles.textareaStyles}
            placeholder="Décrivez votre projet ou posez votre question..."
          />
        </div>

        <div>
          <label className={styles.labelStyles}>
            Vérification <span className={styles.requiredStyles}>*</span>
          </label>
          <ReCAPTCHA
            ref={recaptchaRef}
            sitekey={process.env.GATSBY_RECAPTCHA_SITE_KEY || ""}
            onChange={(token) => setRecaptchaToken(token)}
            onExpired={() => setRecaptchaToken(null)}
            onError={() => setRecaptchaToken(null)}
          />
        </div>

        <Box paddingY={2}>
          <button
            type="submit"
            className={styles.submitButtonStyles}
            disabled={isSubmitting}
          >
            {isSubmitting ? "Envoi en cours..." : "Envoyer le message"}
          </button>
        </Box>

        <Text variant="small" center>
          <span className={styles.requiredStyles}>*</span> Champs obligatoires
        </Text>
      </form>
    </Box>
  )
}
