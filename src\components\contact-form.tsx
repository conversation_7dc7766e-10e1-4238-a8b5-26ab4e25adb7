import * as React from "react"
import { Box, Text } from "./ui"
import * as styles from "./contact-form.css"

export default function ContactForm() {
  const [isSubmitted, setIsSubmitted] = React.useState(false)
  const [isSubmitting, setIsSubmitting] = React.useState(false)
  const [captchaQuestion, setCaptchaQuestion] = React.useState({ num1: 0, num2: 0, answer: 0 })
  const [captchaInput, setCaptchaInput] = React.useState("")

  // Generate new captcha question
  const generateCaptcha = React.useCallback(() => {
    const num1 = Math.floor(Math.random() * 10) + 1
    const num2 = Math.floor(Math.random() * 10) + 1
    const answer = num1 + num2
    setCaptchaQuestion({ num1, num2, answer })
    setCaptchaInput("")
  }, [])

  // Generate captcha on component mount
  React.useEffect(() => {
    generateCaptcha()
  }, [generateCaptcha])

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    // Validate captcha
    if (parseInt(captchaInput) !== captchaQuestion.answer) {
      alert("Veuillez résoudre correctement le calcul de vérification.")
      generateCaptcha() // Generate new captcha on failed attempt
      return
    }

    setIsSubmitting(true)

    const form = e.currentTarget
    const formData = new FormData(form)

    try {
      const response = await fetch("/", {
        method: "POST",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: new URLSearchParams(formData as any).toString(),
      })

      if (response.ok) {
        setIsSubmitted(true)
        form.reset()
        setCaptchaInput("")
        generateCaptcha() // Generate new captcha after successful submission
        // Hide success message after 5 seconds
        setTimeout(() => setIsSubmitted(false), 5000)
      } else {
        throw new Error("Form submission failed")
      }
    } catch (error) {
      alert("Une erreur s'est produite. Veuillez réessayer.")
      generateCaptcha() // Generate new captcha on error
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Box paddingY={4}>
      {isSubmitted && (
        <div className={styles.successMessageStyles}>
          <Text center>
            ✅ Merci ! Votre message a été envoyé avec succès. Nous vous répondrons bientôt.
          </Text>
        </div>
      )}

      <form
        name="contact"
        method="POST"
        data-netlify="true"
        data-netlify-honeypot="bot-field"
        className={styles.formStyles}
        onSubmit={handleSubmit}
      >
        {/* Hidden field for Netlify */}
        <input type="hidden" name="form-name" value="contact" />

        {/* Honeypot field for spam protection */}
        <div style={{ display: "none" }}>
          <label>
            Don't fill this out if you're human: <input name="bot-field" />
          </label>
        </div>

        <div className={styles.formRowStyles}>
          <div className={styles.formFieldStyles}>
            <label htmlFor="name" className={styles.labelStyles}>
              Nom <span className={styles.requiredStyles}>*</span>
            </label>
            <input
              type="text"
              id="name"
              name="name"
              required
              className={styles.inputStyles}
              placeholder="Votre nom complet"
            />
          </div>

          <div className={styles.formFieldStyles}>
            <label htmlFor="email" className={styles.labelStyles}>
              Email <span className={styles.requiredStyles}>*</span>
            </label>
            <input
              type="email"
              id="email"
              name="email"
              required
              className={styles.inputStyles}
              placeholder="<EMAIL>"
            />
          </div>
        </div>

        <div>
          <label htmlFor="subject" className={styles.labelStyles}>
            Sujet <span className={styles.requiredStyles}>*</span>
          </label>
          <input
            type="text"
            id="subject"
            name="subject"
            required
            className={styles.inputStyles}
            placeholder="Objet de votre message"
          />
        </div>

        <div>
          <label htmlFor="message" className={styles.labelStyles}>
            Message <span className={styles.requiredStyles}>*</span>
          </label>
          <textarea
            id="message"
            name="message"
            required
            className={styles.textareaStyles}
            placeholder="Décrivez votre projet ou posez votre question..."
          />
        </div>

        <div>
          <label htmlFor="captcha" className={styles.labelStyles}>
            Vérification <span className={styles.requiredStyles}>*</span>
          </label>
          <div style={{ display: "flex", alignItems: "center", gap: "8px", marginBottom: "8px" }}>
            <span style={{ fontSize: "18px", fontWeight: "bold" }}>
              {captchaQuestion.num1} + {captchaQuestion.num2} = ?
            </span>
            <button
              type="button"
              onClick={generateCaptcha}
              style={{
                background: "none",
                border: "1px solid #ccc",
                borderRadius: "4px",
                padding: "4px 8px",
                cursor: "pointer",
                fontSize: "12px"
              }}
              title="Générer un nouveau calcul"
            >
              🔄
            </button>
          </div>
          <input
            type="number"
            id="captcha"
            name="captcha"
            required
            className={styles.inputStyles}
            placeholder="Entrez le résultat"
            value={captchaInput}
            onChange={(e) => setCaptchaInput(e.target.value)}
            style={{ maxWidth: "120px" }}
          />
        </div>

        <Box paddingY={2}>
          <button
            type="submit"
            className={styles.submitButtonStyles}
            disabled={isSubmitting}
          >
            {isSubmitting ? "Envoi en cours..." : "Envoyer le message"}
          </button>
        </Box>

        <Text variant="small" center>
          <span className={styles.requiredStyles}>*</span> Champs obligatoires
        </Text>
      </form>
    </Box>
  )
}
