import * as React from "react"
import { Box, Text } from "./ui"
import { style } from "@vanilla-extract/css"
import { theme } from "../theme.css"

const formStyles = style({
  display: "flex",
  flexDirection: "column",
  gap: theme.space[3],
  maxWidth: "600px",
  margin: "0 auto",
})

const inputStyles = style({
  padding: theme.space[3],
  border: `1px solid ${theme.colors.border}`,
  borderRadius: theme.radii.default,
  fontSize: theme.fontSizes[2],
  fontFamily: theme.fonts.text,
  width: "100%",
  boxSizing: "border-box",
  ":focus": {
    outline: "none",
    borderColor: theme.colors.primary,
    boxShadow: `0 0 0 2px ${theme.colors.primary}20`,
  },
})

const textareaStyles = style([
  inputStyles,
  {
    minHeight: "120px",
    resize: "vertical",
  },
])

const labelStyles = style({
  fontSize: theme.fontSizes[2],
  fontWeight: theme.fontWeights.medium,
  marginBottom: theme.space[1],
  display: "block",
})

const requiredStyles = style({
  color: theme.colors.primary,
})

export default function ContactForm() {
  return (
    <Box paddingY={4}>
      <form
        name="contact"
        method="POST"
        action="/contact-success"
        data-netlify="true"
        data-netlify-honeypot="bot-field"
        className={formStyles}
      >
        {/* Hidden field for Netlify */}
        <input type="hidden" name="form-name" value="contact" />
        
        {/* Honeypot field for spam protection */}
        <div style={{ display: "none" }}>
          <label>
            Don't fill this out if you're human: <input name="bot-field" />
          </label>
        </div>

        <div>
          <label htmlFor="name" className={labelStyles}>
            Nom <span className={requiredStyles}>*</span>
          </label>
          <input
            type="text"
            id="name"
            name="name"
            required
            className={inputStyles}
            placeholder="Votre nom complet"
          />
        </div>

        <div>
          <label htmlFor="email" className={labelStyles}>
            Email <span className={requiredStyles}>*</span>
          </label>
          <input
            type="email"
            id="email"
            name="email"
            required
            className={inputStyles}
            placeholder="<EMAIL>"
          />
        </div>

        <div>
          <label htmlFor="phone" className={labelStyles}>
            Téléphone
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            className={inputStyles}
            placeholder="Votre numéro de téléphone"
          />
        </div>

        <div>
          <label htmlFor="subject" className={labelStyles}>
            Sujet <span className={requiredStyles}>*</span>
          </label>
          <input
            type="text"
            id="subject"
            name="subject"
            required
            className={inputStyles}
            placeholder="Objet de votre message"
          />
        </div>

        <div>
          <label htmlFor="message" className={labelStyles}>
            Message <span className={requiredStyles}>*</span>
          </label>
          <textarea
            id="message"
            name="message"
            required
            className={textareaStyles}
            placeholder="Décrivez votre projet ou posez votre question..."
          />
        </div>

        <Box paddingY={2}>
          <button
            type="submit"
            className={inputStyles}
            style={{
              backgroundColor: theme.colors.primary,
              color: "white",
              border: "none",
              cursor: "pointer",
              fontWeight: theme.fontWeights.medium,
            }}
          >
            Envoyer le message
          </button>
        </Box>

        <Text variant="small" center>
          <span className={requiredStyles}>*</span> Champs obligatoires
        </Text>
      </form>
    </Box>
  )
}
