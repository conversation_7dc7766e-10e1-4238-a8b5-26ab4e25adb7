import * as React from "react"
import { Box, Text } from "./ui"
import * as styles from "./contact-form.css"

export default function ContactForm() {
  return (
    <Box paddingY={4}>
      <form
        name="contact"
        method="POST"
        action="/contact-success"
        data-netlify="true"
        data-netlify-honeypot="bot-field"
        className={styles.formStyles}
      >
        {/* Hidden field for Netlify */}
        <input type="hidden" name="form-name" value="contact" />

        {/* Honeypot field for spam protection */}
        <div style={{ display: "none" }}>
          <label>
            Don't fill this out if you're human: <input name="bot-field" />
          </label>
        </div>

        <div>
          <label htmlFor="name" className={styles.labelStyles}>
            Nom <span className={styles.requiredStyles}>*</span>
          </label>
          <input
            type="text"
            id="name"
            name="name"
            required
            className={styles.inputStyles}
            placeholder="Votre nom complet"
          />
        </div>

        <div>
          <label htmlFor="email" className={styles.labelStyles}>
            Email <span className={styles.requiredStyles}>*</span>
          </label>
          <input
            type="email"
            id="email"
            name="email"
            required
            className={styles.inputStyles}
            placeholder="<EMAIL>"
          />
        </div>

        <div>
          <label htmlFor="phone" className={styles.labelStyles}>
            Téléphone
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            className={styles.inputStyles}
            placeholder="Votre numéro de téléphone"
          />
        </div>

        <div>
          <label htmlFor="subject" className={styles.labelStyles}>
            Sujet <span className={styles.requiredStyles}>*</span>
          </label>
          <input
            type="text"
            id="subject"
            name="subject"
            required
            className={styles.inputStyles}
            placeholder="Objet de votre message"
          />
        </div>

        <div>
          <label htmlFor="message" className={styles.labelStyles}>
            Message <span className={styles.requiredStyles}>*</span>
          </label>
          <textarea
            id="message"
            name="message"
            required
            className={styles.textareaStyles}
            placeholder="Décrivez votre projet ou posez votre question..."
          />
        </div>

        <Box paddingY={2}>
          <button
            type="submit"
            className={styles.submitButtonStyles}
          >
            Envoyer le message
          </button>
        </Box>

        <Text variant="small" center>
          <span className={styles.requiredStyles}>*</span> Champs obligatoires
        </Text>
      </form>
    </Box>
  )
}
