{"name": "gatsby-starter-datocms-homepage-ts", "version": "1.0.0", "main": "index.js", "engines": {"node": ">=18.0.0"}, "scripts": {"start": "gatsby develop", "develop": "gatsby develop", "build": "gatsby build", "serve": "gatsby serve", "clean": "gatsby clean", "setup": "node ./scripts/setup.js"}, "dependencies": {"@fontsource/dm-mono": "^4.5.9", "@fontsource/dm-sans": "^4.5.8", "@types/react-google-recaptcha": "^2.1.9", "@vanilla-extract/babel-plugin": "^1.2.0", "@vanilla-extract/css": "^1.9.0", "@vanilla-extract/css-utils": "^0.1.2", "@vanilla-extract/webpack-plugin": "^2.1.12", "datocms-structured-text-to-html-string": "^2.1.3", "gatsby": "^5.0.0", "gatsby-plugin-clarity": "^1.0.1", "gatsby-plugin-google-gtag": "^5.13.1", "gatsby-plugin-image": "^3.0.0", "gatsby-plugin-manifest": "^5.0.0-next.4", "gatsby-plugin-sharp": "^5.0.0", "gatsby-plugin-vanilla-extract": "^4.0.1", "gatsby-source-datocms": "^5.0.0-3", "gatsby-source-filesystem": "^5.0.0", "gatsby-transformer-sharp": "^5.0.0", "is-absolute-url": "^4.0.1", "react": "^18.0.0", "react-dom": "^18.0.0", "react-feather": "^2.0.9", "react-google-recaptcha": "^3.1.0", "sharp": "^0.30.5"}, "devDependencies": {"@types/gtag.js": "^0.0.20", "ajv": "^7.2.4", "chalk": "^4.1.0", "husky": "^7.0.4", "inquirer": "^8.2.0", "lint-staged": "^12.3.5", "prettier": "2.5.1", "typescript": "^4.6.2", "yargs": "^17.3.1"}}