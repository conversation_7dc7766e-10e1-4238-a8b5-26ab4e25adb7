import * as React from "react"
import { graphql } from "gatsby"
import Layout from "../components/layout"
import { Container, Box, Heading, Text } from "../components/ui"
import SEOHead from "../components/head"
import * as styles from "./page.css"

interface PageProps {
  data: {
    page: {
      id: string
      title: string
      slug: string
      description: string
      image: { id: string; url: string }
      html: string
    }
  }
}

export default function Page(props: PageProps) {
  const { page } = props.data

  // Check if the content contains the contact form placeholder
  const hasContactForm = page.html.includes("{{#contactForm#}}")

  // Split content around the contact form placeholder
  const contentParts = hasContactForm
    ? page.html.split("{{#contactForm#}}")
    : [page.html]

  return (
    <Layout>
      <Box paddingY={5}>
        <Container width="narrow">
          <Heading as="h1">{page.title}</Heading>

          {hasContactForm ? (
            <>
              {/* Content before contact form */}
              {contentParts[0] && (
                <div
                  dangerouslySetInnerHTML={{
                    __html: contentParts[0],
                  }}
                />
              )}

              {/* Email contact information */}
              <div className={styles.contactSection}>
                <Heading as="h3" className={styles.contactHeading}>
                  Contactez-nous
                </Heading>
                <Text variant="lead" className={styles.contactDescription}>
                  Pour toute question ou demande d'information, n'hésitez pas à nous contacter :
                </Text>
                <div>
                  <a
                    href="mailto:<EMAIL>"
                    className={styles.emailLink}
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>

              {/* Content after contact form */}
              {contentParts[1] && (
                <div
                  dangerouslySetInnerHTML={{
                    __html: contentParts[1],
                  }}
                />
              )}
            </>
          ) : (
            <div
              dangerouslySetInnerHTML={{
                __html: page.html,
              }}
            />
          )}
        </Container>
      </Box>
    </Layout>
  )
}
export const Head = (props: PageProps) => {
  const { page } = props.data
  return <SEOHead {...page} />
}
export const query = graphql`
  query PageContent($id: String!) {
    page(id: { eq: $id }) {
      id
      title
      slug
      description
      image {
        id
        url
      }
      html
    }
  }
`
